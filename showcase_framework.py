"""
Lightweight, production-oriented vision showcase framework.

This is a patched version of the original `showcase_framework.py` with a fix for
`OpenCVFileSink` so the output video respects the requested FPS.

Key changes:
 - GStreamer pipeline now includes explicit caps for framerate, resolution and
   uses `videorate` and `h264parse` to ensure correct timing when using GStreamer.
 - Added timestamping / `appsrc` properties to make the pipeline "is-live" and
   accept time-based frames.
 - On top of the pipeline fix, the sink uses a time-based write throttle to
   ensure frames are written at (approximately) the requested FPS. This acts as
   a safety-net when GStreamer is not available or the fallback writer is used.
 - A small helper converts float FPS to a rational string (e.g. `30/1` or
   `30000/1001`) to satisfy GStreamer caps.
 - The rest of the framework is unchanged.

If you still see FPS mismatch after this change, try running with the fallback
XVID writer (rename output to .avi) or verify the pipeline has GStreamer with
H.264 support installed on the host.
"""

from __future__ import annotations

import abc
import configparser
import logging
import math
import threading
import time
import queue
from dataclasses import dataclass
from typing import Any, Dict, Iterable, List, Optional, Tuple, Union
from functools import lru_cache
from weakref import WeakKeyDictionary

import cv2
import numpy as np

# Optional supervision import with graceful fallback
try:
    import supervision as sv
    SUPERVISION_AVAILABLE = True
except ImportError:
    sv = None
    SUPERVISION_AVAILABLE = False

logger = logging.getLogger("showcase_framework")
logging.basicConfig(level=logging.INFO, format="[%(levelname)s] %(message)s")


# ----------------------------- Core dataclasses / types -----------------------------

@dataclass
class Detection:
    """Normalized representation of a single detection.

    bbox: (x1, y1, x2, y2) in *absolute pixel coordinates* when `normalized` is False,
          otherwise values are 0..1 normalized coordinates.
    score: optional confidence score (0..1 or model's native scale)
    label: optional string label (e.g. 'person', 'car')
    raw: the original model item for debugging
    """
    bbox: Tuple[float, float, float, float]
    score: Optional[float] = None
    label: Optional[str] = None
    raw: Any = None


@dataclass
class FramePacket:
    """One packet that flows through the pipeline."""
    frame_rgb: np.ndarray  # HxWx3 uint8 RGB
    model_out: Any
    timestamp: float
    frame_idx: int
    meta: Dict[str, Any]


# ----------------------------- Abstract base classes -----------------------------

class FrameSource(abc.ABC):
    """Abstract frame provider.

    Implementations must provide `read()` to return RGB numpy frames (HxWx3) or None on EOF.
    """

    @abc.abstractmethod
    def read(self) -> Optional[np.ndarray]:
        pass

    @abc.abstractmethod
    def start(self) -> None:
        pass

    @abc.abstractmethod
    def stop(self) -> None:
        pass


class InferenceModel(abc.ABC):
    """Abstract inference model.

    Implement `infer(frame_rgb)` and return model-specific output. The framework will
    normalize that output into `Detection` objects for processors via helpers.
    """

    @abc.abstractmethod
    def infer(self, frame_rgb: np.ndarray) -> Any:
        pass

    def warmup(self, frame_rgb: np.ndarray) -> None:
        """Optional warmup - default is no-op."""
        return


class FrameProcessor(abc.ABC):
    """Business-logic class. Implement `process(packet)` to return processed RGB frame.

    The base class provides helpers: `parse_detections` and `draw_boxes` to avoid
    reimplementing parsing/drawing across showcases.
    """

    def __init__(self, conf_threshold: float = 0.0):
        self.conf_threshold = float(conf_threshold or 0.0)

    @abc.abstractmethod
    def process(self, pkt: FramePacket) -> np.ndarray:
        """Return processed RGB frame (HxWx3 uint8)."""
        pass

    # ----------------- Helpers for processors -----------------
    def parse_detections(self, model_out: Any, frame_shape: Tuple[int, int]) -> List[Detection]:
        """Normalize arbitrary model outputs into a list of Detection.

        Accepts common shapes:
          - list/tuple of dicts or arrays
          - dict with keys 'results', 'detections', 'objects', 'boxes'
          - numpy arrays (N,4) or (N,5)
          - single dict with 'bbox'/'box' and optional 'score'/'confidence' and 'label'/'class'
        """
        h, w = frame_shape
        raw_items = _explode_model_output(model_out)
        dets: List[Detection] = []
        for item in raw_items:
            try:
                bbox_raw, score, label = _extract_bbox_score_label(item)
                if bbox_raw is None:
                    continue
                x1, y1, x2, y2 = _to_xyxy_pixels(bbox_raw, w, h)
                det = Detection(bbox=(x1, y1, x2, y2), score=score, label=label, raw=item)
                if det.score is None or det.score >= self.conf_threshold:
                    dets.append(det)
            except Exception as e:
                logger.debug("Failed parsing item %s: %s", getattr(item, '__repr__', lambda: item)(), e)
                continue
        return dets

    def draw_boxes(
        self,
        frame_rgb: np.ndarray,
        detections: Iterable[Detection],
        label_field: bool = True,
        score_field: bool = True,
        thickness: int = 2,
    ) -> np.ndarray:
        """Draw boxes (in-place on a copy) and return RGB frame.

        Colors are generated deterministically per label to be stable across frames.
        """
        out = frame_rgb.copy()
        if out.dtype != np.uint8:
            out = (np.clip(out, 0, 255)).astype(np.uint8)

        # OpenCV expects BGR for drawing
        bgr = cv2.cvtColor(out, cv2.COLOR_RGB2BGR)
        h, w = out.shape[:2]

        for det in detections:
            x1, y1, x2, y2 = int(det.bbox[0]), int(det.bbox[1]), int(det.bbox[2]), int(det.bbox[3])
            # clamp
            x1, x2 = max(0, min(w - 1, x1)), max(0, min(w - 1, x2))
            y1, y2 = max(0, min(h - 1, y1)), max(0, min(h - 1, y2))
            label = det.label or ""
            color = _label_color(label)
            try:
                cv2.rectangle(bgr, (x1, y1), (x2, y2), color, thickness)
                caption_parts = []
                if label_field and label:
                    caption_parts.append(str(label))
                if score_field and det.score is not None:
                    try:
                        caption_parts.append(f"{float(det.score):.2f}")
                    except Exception:
                        caption_parts.append(str(det.score))
                if caption_parts:
                    caption = " ".join(caption_parts)
                    cv2.putText(bgr, caption, (x1, max(0, y1 - 6)), cv2.FONT_HERSHEY_SIMPLEX, 0.5, color, 1)
            except Exception as e:
                logger.debug("Failed to draw box: %s", e)
                continue

        return cv2.cvtColor(bgr, cv2.COLOR_BGR2RGB)


class FrameSink(abc.ABC):
    """Abstract sink - e.g. file writer, RTSP, cloud upload."""

    @abc.abstractmethod
    def start(self) -> None:
        pass

    @abc.abstractmethod
    def put(self, frame_rgb: np.ndarray) -> None:
        pass

    @abc.abstractmethod
    def stop(self) -> None:
        pass


# ----------------------------- Utilities: parsing + colors -----------------------------

def _explode_model_output(model_out: Any) -> List[Any]:
    """Return a flat list of items to interpret as detections.

    This unwraps common container keys ('results', 'detections', 'objects', 'boxes') and
    accepts lists, tuples, numpy arrays, and single dicts.
    """
    if model_out is None:
        return []

    # If an object exposes .results or .detections use that
    for key in ("results", "detections", "objects", "boxes"):
        if hasattr(model_out, key):
            try:
                container = getattr(model_out, key)
                return list(container) if container is not None else []
            except Exception:
                break

    # If dict with one of those keys
    if isinstance(model_out, dict):
        for key in ("results", "detections", "objects", "boxes"):
            if key in model_out and isinstance(model_out[key], (list, tuple, np.ndarray)):
                return list(model_out[key])
        # Single dict that may itself be a detection
        return [model_out]

    # If list/tuple/ndarray: expand to list
    if isinstance(model_out, (list, tuple)):
        return list(model_out)

    if isinstance(model_out, np.ndarray):
        if model_out.ndim == 2 and model_out.shape[1] in (4, 5):
            return [row for row in model_out]
        if model_out.ndim == 1 and model_out.size >= 4:
            return [model_out]
        # fallback: try to convert rows
        try:
            return [row for row in model_out]
        except Exception:
            return [model_out]

    # Unknown object: wrap
    return [model_out]


def _extract_bbox_score_label(item: Any) -> Tuple[Optional[Any], Optional[float], Optional[str]]:
    """Try to pull bbox, score, and label from an item.

    Bbox returned in raw form; caller will convert to pixels.
    """
    # If it's already a Detection
    if isinstance(item, Detection):
        return item.bbox, item.score, item.label

    # Numpy row or list-like: [x1,y1,x2,y2,(score),(class/label)]
    if isinstance(item, (list, tuple, np.ndarray)):
        arr = np.asarray(item).flatten()
        if arr.size >= 4:
            bbox = arr[:4].tolist()
            score = float(arr[4]) if arr.size >= 5 else None
            label = None
            if arr.size >= 6:
                label = str(arr[5])
            return bbox, score, label
        return None, None, None

    # Dict-like
    if isinstance(item, dict):
        # Score keys
        score = None
        for k in ("score", "confidence", "prob", "conf"):
            if k in item:
                try:
                    score = float(item[k])
                except Exception:
                    score = None
                break
        # Label/class keys
        label = None
        for k in ("label", "class", "class_name", "category"):
            if k in item:
                label = str(item[k])
                break
        # bbox keys
        if "bbox" in item:
            return item["bbox"], score, label
        if "box" in item:
            return item["box"], score, label
        # individual fields
        if all(k in item for k in ("x1", "y1", "x2", "y2")):
            return (item["x1"], item["y1"], item["x2"], item["y2"]), score, label
        if all(k in item for k in ("x", "y", "w", "h")):
            return (item["x"], item["y"], item["x"] + item["w"], item["y"] + item["h"]), score, label
        # Sometimes a single dict holds nested detections (handled earlier) - otherwise unknown
        return None, score, label

    # Unknown item
    return None, None, None


def _to_xyxy_pixels(bbox_raw: Any, frame_w: int, frame_h: int) -> Tuple[int, int, int, int]:
    """Convert bbox in raw form to absolute pixel (x1,y1,x2,y2).

    Accepts:
      - sequences of 4 numbers: [x1,y1,x2,y2] or [x,y,w,h] or normalized 0..1 versions
      - dicts handled earlier (we receive sequences here)
    """
    arr = np.asarray(bbox_raw, dtype=float).flatten()
    if arr.size < 4:
        raise ValueError("bbox must have at least 4 numeric values")

    # If they look normalized (all values in 0..1.05) treat as normalized xyxy
    if np.all(arr[:4] >= 0.0) and np.all(arr[:4] <= 1.05):
        x1 = int(round(float(arr[0]) * frame_w))
        y1 = int(round(float(arr[1]) * frame_h))
        x2 = int(round(float(arr[2]) * frame_w))
        y2 = int(round(float(arr[3]) * frame_h))
        return x1, y1, x2, y2

    # Otherwise interpret as absolute coords, but detect xywh if arr[2] < arr[0] (common when models return x,w)
    x0, y0, x2_or_w, y2_or_h = float(arr[0]), float(arr[1]), float(arr[2]), float(arr[3])
    # Heuristic: if x2_or_w < x0 or y2_or_h < y0 -> treat as x,y,w,h
    if (x2_or_w < x0) or (y2_or_h < y0):
        x1 = int(round(x0))
        y1 = int(round(y0))
        x2 = int(round(x0 + x2_or_w))
        y2 = int(round(y0 + y2_or_h))
        return x1, y1, x2, y2

    # Otherwise assume x1,y1,x2,y2 absolute
    x1 = int(round(x0))
    y1 = int(round(y0))
    x2 = int(round(x2_or_w))
    y2 = int(round(y2_or_h))
    return x1, y1, x2, y2


def _label_color(label: Optional[str]) -> Tuple[int, int, int]:
    """Deterministic color for a label. Returns a BGR tuple for OpenCV drawing.

    We use a hash -> HSV -> BGR mapping to give varied but steady colors.
    """
    if not label:
        # green-ish default
        return (0, 200, 0)
    h = abs(hash(label)) % 360
    # convert hue to BGR via simple HSV->BGR (approx)
    import colorsys

    r, g, b = colorsys.hsv_to_rgb(h / 360.0, 0.7, 0.9)
    # Convert 0..1 -> 0..255 and BGR ordering
    return (int(b * 255), int(g * 255), int(r * 255))


# ----------------------------- Supervision Integration Layer -----------------------------

class SupervisionConverter:
    """
    High-performance conversion layer between various detection formats and Supervision library.

    This class provides a clean, extensible interface for converting detection results
    from different sources (Hailo, YOLO, custom formats) to supervision.Detections format
    and vice versa. It implements intelligent caching and follows SOLID principles.

    Features:
    - Automatic format detection and conversion
    - Intelligent caching to avoid redundant computations
    - Extensible design for adding new detection sources
    - Comprehensive error handling for edge cases
    - Metadata preservation during conversion
    - Thread-safe operations
    """

    def __init__(self, enable_caching: bool = True, cache_size: int = 128):
        """
        Initialize the SupervisionConverter.

        Args:
            enable_caching: Whether to enable intelligent caching for performance
            cache_size: Maximum number of cached conversion results
        """
        self.enable_caching = enable_caching
        self.cache_size = cache_size
        self._conversion_cache = {} if enable_caching else None
        self._cache_hits = 0
        self._cache_misses = 0

        # Thread safety
        self._lock = threading.RLock()

        # Supported detection source formats
        self._supported_formats = {
            'hailo': self._convert_hailo_to_supervision,
            'degirum': self._convert_hailo_to_supervision,  # Degirum uses similar format
            'yolo': self._convert_yolo_to_supervision,
            'generic': self._convert_generic_to_supervision,
            'framework': self._convert_framework_detections_to_supervision
        }

        logger.info(f"SupervisionConverter initialized (caching={'enabled' if enable_caching else 'disabled'}, "
                   f"supervision={'available' if SUPERVISION_AVAILABLE else 'unavailable'})")

    def is_supervision_available(self) -> bool:
        """Check if supervision library is available."""
        return SUPERVISION_AVAILABLE

    def get_cache_stats(self) -> Dict[str, int]:
        """Get caching statistics for performance monitoring."""
        with self._lock:
            return {
                'hits': self._cache_hits,
                'misses': self._cache_misses,
                'hit_rate': self._cache_hits / max(1, self._cache_hits + self._cache_misses),
                'cache_size': len(self._conversion_cache) if self._conversion_cache else 0
            }

    def clear_cache(self) -> None:
        """Clear the conversion cache."""
        with self._lock:
            if self._conversion_cache:
                self._conversion_cache.clear()
            self._cache_hits = 0
            self._cache_misses = 0
            logger.debug("SupervisionConverter cache cleared")

    def convert_to_supervision(
        self,
        detections: Any,
        source_format: str = 'auto',
        frame_shape: Optional[Tuple[int, int]] = None,
        class_names: Optional[List[str]] = None,
        preserve_metadata: bool = True
    ) -> Optional["sv.Detections"]:
        """
        Convert detections from various formats to supervision.Detections.

        Args:
            detections: Detection results in various formats
            source_format: Source format ('hailo', 'yolo', 'generic', 'auto')
            frame_shape: (height, width) for coordinate normalization
            class_names: Optional class names for detections
            preserve_metadata: Whether to preserve additional metadata

        Returns:
            supervision.Detections object or None if conversion fails
        """
        if not SUPERVISION_AVAILABLE:
            logger.warning("Supervision library not available. Install with: pip install supervision")
            return None

        if detections is None:
            return sv.Detections.empty()

        # Auto-detect format if requested
        if source_format == 'auto':
            source_format = self._detect_format(detections)

        # Generate cache key for performance
        cache_key = None
        if self.enable_caching:
            cache_key = self._generate_cache_key(detections, source_format, frame_shape, class_names)

            with self._lock:
                if cache_key in self._conversion_cache:
                    self._cache_hits += 1
                    return self._conversion_cache[cache_key]
                else:
                    self._cache_misses += 1

        try:
            # Perform conversion based on detected/specified format
            converter_func = self._supported_formats.get(source_format, self._convert_generic_to_supervision)
            sv_detections = converter_func(detections, frame_shape, class_names, preserve_metadata)

            # Cache the result if caching is enabled
            if self.enable_caching and cache_key and sv_detections is not None:
                with self._lock:
                    # Implement simple LRU by removing oldest entries
                    if len(self._conversion_cache) >= self.cache_size:
                        # Remove oldest entry (simple FIFO for performance)
                        oldest_key = next(iter(self._conversion_cache))
                        del self._conversion_cache[oldest_key]

                    self._conversion_cache[cache_key] = sv_detections

            return sv_detections

        except Exception as e:
            logger.error(f"Failed to convert detections from {source_format} format: {e}")
            return None

    def convert_from_supervision(
        self,
        sv_detections: "sv.Detections",
        target_format: str = 'hailo',
        preserve_metadata: bool = True
    ) -> List[Dict[str, Any]]:
        """
        Convert supervision.Detections back to various formats.

        Args:
            sv_detections: supervision.Detections object
            target_format: Target format ('hailo', 'yolo', 'generic')
            preserve_metadata: Whether to preserve additional metadata

        Returns:
            List of detection dictionaries in target format
        """
        if not SUPERVISION_AVAILABLE or sv_detections is None:
            return []

        if len(sv_detections) == 0:
            return []

        try:
            if target_format == 'hailo' or target_format == 'degirum':
                return self._convert_supervision_to_hailo(sv_detections, preserve_metadata)
            elif target_format == 'yolo':
                return self._convert_supervision_to_yolo(sv_detections, preserve_metadata)
            else:
                return self._convert_supervision_to_generic(sv_detections, preserve_metadata)

        except Exception as e:
            logger.error(f"Failed to convert from supervision to {target_format} format: {e}")
            return []

    def _detect_format(self, detections: Any) -> str:
        """
        Automatically detect the format of detection results.

        Args:
            detections: Detection results in unknown format

        Returns:
            Detected format string
        """
        if isinstance(detections, list) and len(detections) > 0:
            first_det = detections[0]

            # Check for Hailo/Degirum format
            if isinstance(first_det, dict):
                if 'bbox' in first_det and 'score' in first_det:
                    return 'hailo'
                elif 'box' in first_det and 'confidence' in first_det:
                    return 'yolo'
                elif all(k in first_det for k in ('x1', 'y1', 'x2', 'y2')):
                    return 'generic'

            # Check for framework Detection objects
            elif hasattr(first_det, 'bbox') and hasattr(first_det, 'score'):
                return 'framework'

        # Check for object with results attribute (Degirum style)
        elif hasattr(detections, 'results'):
            return 'hailo'

        return 'generic'

    def _generate_cache_key(
        self,
        detections: Any,
        source_format: str,
        frame_shape: Optional[Tuple[int, int]],
        class_names: Optional[List[str]]
    ) -> str:
        """Generate a cache key for detection conversion results."""
        try:
            # Create a simple hash-based key
            det_hash = hash(str(detections)[:100])  # Limit string length for performance
            shape_hash = hash(frame_shape) if frame_shape else 0
            names_hash = hash(tuple(class_names)) if class_names else 0

            return f"{source_format}_{det_hash}_{shape_hash}_{names_hash}"
        except Exception:
            # Fallback to timestamp-based key if hashing fails
            return f"{source_format}_{time.time()}"

    def _convert_hailo_to_supervision(
        self,
        detections: Any,
        frame_shape: Optional[Tuple[int, int]],
        class_names: Optional[List[str]],
        preserve_metadata: bool
    ) -> "sv.Detections":
        """Convert Hailo/Degirum detection format to supervision.Detections."""
        xyxy_list = []
        confidence_list = []
        class_id_list = []
        metadata = {}

        # Handle object with .results attribute (Degirum style)
        if hasattr(detections, 'results'):
            detection_list = detections.results
        elif isinstance(detections, list):
            detection_list = detections
        else:
            detection_list = [detections]

        for i, det in enumerate(detection_list):
            try:
                # Extract bounding box
                bbox = det.get('bbox') or det.get('box')
                if bbox is None or len(bbox) != 4:
                    continue

                # Extract confidence score
                score = det.get('score') or det.get('confidence', 0.0)

                # Convert bbox to xyxy format
                x1, y1, x2, y2 = bbox

                # Handle normalized coordinates
                if frame_shape and all(0 <= coord <= 1.05 for coord in bbox):
                    h, w = frame_shape
                    x1, x2 = x1 * w, x2 * w
                    y1, y2 = y1 * h, y2 * h

                # Handle xywh format (if x2 < x1 or y2 < y1)
                if x2 < x1 or y2 < y1:
                    x2, y2 = x1 + x2, y1 + y2

                xyxy_list.append([x1, y1, x2, y2])
                confidence_list.append(float(score))
                class_id_list.append(0)  # Default class ID

                # Preserve metadata if requested
                if preserve_metadata:
                    for key, value in det.items():
                        if key not in ('bbox', 'box', 'score', 'confidence'):
                            if key not in metadata:
                                metadata[key] = []
                            metadata[key].append(value)

            except Exception as e:
                logger.debug(f"Failed to parse detection {i}: {e}")
                continue

        if not xyxy_list:
            return sv.Detections.empty()

        # Create supervision detections
        xyxy = np.array(xyxy_list, dtype=np.float32)
        confidence = np.array(confidence_list, dtype=np.float32)
        class_id = np.array(class_id_list, dtype=int)

        # Add class names if provided
        if class_names:
            if len(class_names) == len(xyxy_list):
                metadata['class_name'] = np.array(class_names)
            else:
                metadata['class_name'] = np.array(['detection'] * len(xyxy_list))
        else:
            metadata['class_name'] = np.array(['detection'] * len(xyxy_list))

        # Ensure all metadata arrays have the same length as detections
        final_metadata = {}
        for key, values in metadata.items():
            if len(values) == len(xyxy_list):
                if isinstance(values, list) and len(values) > 0:
                    # Convert to numpy array with appropriate dtype
                    if isinstance(values[0], str):
                        final_metadata[key] = np.array(values, dtype=object)
                    else:
                        final_metadata[key] = np.array(values)
                else:
                    final_metadata[key] = values
            else:
                logger.debug(f"Skipping metadata key '{key}' due to length mismatch: {len(values)} vs {len(xyxy_list)}")

        return sv.Detections(
            xyxy=xyxy,
            confidence=confidence,
            class_id=class_id,
            data=final_metadata
        )

    def _convert_framework_detections_to_supervision(
        self,
        detections: List[Detection],
        frame_shape: Optional[Tuple[int, int]],
        class_names: Optional[List[str]],
        preserve_metadata: bool
    ) -> "sv.Detections":
        """Convert framework Detection objects to supervision.Detections."""
        if not detections:
            return sv.Detections.empty()

        xyxy_list = []
        confidence_list = []
        class_id_list = []
        metadata = {'class_name': []}

        for i, det in enumerate(detections):
            try:
                # Extract data from Detection object
                x1, y1, x2, y2 = det.bbox
                xyxy_list.append([x1, y1, x2, y2])
                confidence_list.append(det.score if det.score is not None else 0.0)
                class_id_list.append(0)  # Default class ID

                # Add class name
                class_name = class_names[i] if class_names and i < len(class_names) else det.label or 'detection'
                metadata['class_name'].append(class_name)

                # Preserve raw data if requested
                if preserve_metadata and det.raw is not None:
                    if 'raw_data' not in metadata:
                        metadata['raw_data'] = []
                    metadata['raw_data'].append(det.raw)

            except Exception as e:
                logger.debug(f"Failed to convert framework detection {i}: {e}")
                continue

        if not xyxy_list:
            return sv.Detections.empty()

        return sv.Detections(
            xyxy=np.array(xyxy_list, dtype=np.float32),
            confidence=np.array(confidence_list, dtype=np.float32),
            class_id=np.array(class_id_list, dtype=int),
            data={k: np.array(v) if isinstance(v[0], str) else v for k, v in metadata.items()}
        )

    def _convert_yolo_to_supervision(
        self,
        detections: Any,
        frame_shape: Optional[Tuple[int, int]],
        class_names: Optional[List[str]],
        preserve_metadata: bool
    ) -> "sv.Detections":
        """Convert YOLO detection format to supervision.Detections."""
        # This is a placeholder for YOLO format conversion
        # Can be extended based on specific YOLO output format
        return self._convert_generic_to_supervision(detections, frame_shape, class_names, preserve_metadata)

    def _convert_generic_to_supervision(
        self,
        detections: Any,
        frame_shape: Optional[Tuple[int, int]],
        class_names: Optional[List[str]],
        preserve_metadata: bool
    ) -> "sv.Detections":
        """Convert generic detection format to supervision.Detections using existing parsing logic."""
        try:
            # Use the existing framework parsing logic
            if frame_shape:
                h, w = frame_shape
                raw_items = _explode_model_output(detections)
                parsed_detections = []

                for item in raw_items:
                    try:
                        bbox_raw, score, label = _extract_bbox_score_label(item)
                        if bbox_raw is None:
                            continue
                        x1, y1, x2, y2 = _to_xyxy_pixels(bbox_raw, w, h)
                        det = Detection(bbox=(x1, y1, x2, y2), score=score, label=label, raw=item)
                        parsed_detections.append(det)
                    except Exception as e:
                        logger.debug(f"Failed parsing generic detection item: {e}")
                        continue

                # Convert parsed detections to supervision format
                return self._convert_framework_detections_to_supervision(
                    parsed_detections, frame_shape, class_names, preserve_metadata
                )
            else:
                logger.warning("Frame shape required for generic detection conversion")
                return sv.Detections.empty()

        except Exception as e:
            logger.error(f"Failed to convert generic detections: {e}")
            return sv.Detections.empty()

    def _convert_supervision_to_hailo(
        self,
        sv_detections: "sv.Detections",
        preserve_metadata: bool
    ) -> List[Dict[str, Any]]:
        """Convert supervision.Detections to Hailo format."""
        hailo_detections = []

        for i in range(len(sv_detections)):
            det_dict = {
                'bbox': sv_detections.xyxy[i].tolist(),
                'score': float(sv_detections.confidence[i])
            }

            # Add metadata if available and requested
            if preserve_metadata and sv_detections.data:
                for key, values in sv_detections.data.items():
                    if key != 'class_name' and i < len(values):
                        det_dict[key] = values[i]

            hailo_detections.append(det_dict)

        return hailo_detections

    def _convert_supervision_to_yolo(
        self,
        sv_detections: "sv.Detections",
        preserve_metadata: bool
    ) -> List[Dict[str, Any]]:
        """Convert supervision.Detections to YOLO format."""
        # Placeholder for YOLO format conversion
        return self._convert_supervision_to_generic(sv_detections, preserve_metadata)

    def _convert_supervision_to_generic(
        self,
        sv_detections: "sv.Detections",
        preserve_metadata: bool
    ) -> List[Dict[str, Any]]:
        """Convert supervision.Detections to generic format."""
        generic_detections = []

        for i in range(len(sv_detections)):
            x1, y1, x2, y2 = sv_detections.xyxy[i]
            det_dict = {
                'x1': float(x1), 'y1': float(y1),
                'x2': float(x2), 'y2': float(y2),
                'confidence': float(sv_detections.confidence[i]),
                'class_id': int(sv_detections.class_id[i])
            }

            # Add class name if available
            if 'class_name' in sv_detections.data and i < len(sv_detections.data['class_name']):
                det_dict['class_name'] = sv_detections.data['class_name'][i]

            # Add other metadata if requested
            if preserve_metadata and sv_detections.data:
                for key, values in sv_detections.data.items():
                    if key not in ('class_name',) and i < len(values):
                        det_dict[key] = values[i]

            generic_detections.append(det_dict)

        return generic_detections


class SupervisionTracker:
    """
    Intelligent tracking wrapper that provides easy integration with various tracking algorithms.

    This class wraps supervision's tracking capabilities and provides a clean interface
    for adding object tracking to any detection workflow. It supports multiple tracking
    algorithms and provides intelligent parameter tuning.
    """

    def __init__(
        self,
        tracker_type: str = 'bytetrack',
        track_activation_threshold: float = 0.25,
        lost_track_buffer: int = 30,
        minimum_matching_threshold: float = 0.8,
        frame_rate: int = 30,
        enable_smoothing: bool = False
    ):
        """
        Initialize the tracking system.

        Args:
            tracker_type: Type of tracker ('bytetrack', 'deepsort', etc.)
            track_activation_threshold: Minimum confidence to start a new track
            lost_track_buffer: Number of frames to keep lost tracks
            minimum_matching_threshold: Matching threshold for track association
            frame_rate: Video frame rate for temporal consistency
            enable_smoothing: Whether to enable detection smoothing
        """
        self.tracker_type = tracker_type
        self.enable_smoothing = enable_smoothing
        self._tracker = None
        self._smoother = None

        # Tracking parameters
        self.track_params = {
            'track_activation_threshold': track_activation_threshold,
            'lost_track_buffer': lost_track_buffer,
            'minimum_matching_threshold': minimum_matching_threshold,
            'frame_rate': frame_rate
        }

        # Initialize tracker if supervision is available
        if SUPERVISION_AVAILABLE:
            self._initialize_tracker()
        else:
            logger.warning("Supervision not available - tracking disabled")

    def _initialize_tracker(self):
        """Initialize the tracking algorithm."""
        try:
            if self.tracker_type.lower() == 'bytetrack':
                self._tracker = sv.ByteTrack(**self.track_params)
            else:
                logger.warning(f"Unsupported tracker type: {self.tracker_type}, using ByteTrack")
                self._tracker = sv.ByteTrack(**self.track_params)

            # Initialize smoother if requested
            if self.enable_smoothing:
                self._smoother = sv.DetectionsSmoother()

            logger.info(f"Initialized {self.tracker_type} tracker with smoothing={'enabled' if self.enable_smoothing else 'disabled'}")

        except Exception as e:
            logger.error(f"Failed to initialize tracker: {e}")
            self._tracker = None

    def is_available(self) -> bool:
        """Check if tracking is available."""
        return self._tracker is not None

    def update_with_detections(self, detections: "sv.Detections") -> "sv.Detections":
        """
        Update tracker with new detections.

        Args:
            detections: supervision.Detections object

        Returns:
            Updated detections with tracking IDs
        """
        if not self.is_available() or detections is None:
            return detections

        try:
            # Update tracker
            tracked_detections = self._tracker.update_with_detections(detections)

            # Apply smoothing if enabled
            if self._smoother is not None:
                tracked_detections = self._smoother.update_with_detections(tracked_detections)

            return tracked_detections

        except Exception as e:
            logger.error(f"Tracking update failed: {e}")
            return detections

    def reset(self):
        """Reset the tracker state."""
        if self._tracker is not None:
            try:
                # Re-initialize tracker to reset state
                self._initialize_tracker()
                logger.debug("Tracker reset successfully")
            except Exception as e:
                logger.error(f"Failed to reset tracker: {e}")


class EnhancedFrameProcessor(FrameProcessor):
    """
    Enhanced FrameProcessor with built-in supervision integration and tracking support.

    This class extends the base FrameProcessor to provide seamless integration with
    the supervision library, including automatic format conversion, tracking, and
    professional visualization capabilities.
    """

    def __init__(
        self,
        conf_threshold: float = 0.0,
        enable_supervision: bool = True,
        enable_tracking: bool = False,
        tracker_config: Optional[Dict[str, Any]] = None,
        enable_caching: bool = True
    ):
        """
        Initialize the enhanced frame processor.

        Args:
            conf_threshold: Confidence threshold for detections
            enable_supervision: Whether to use supervision format and visualization
            enable_tracking: Whether to enable object tracking
            tracker_config: Configuration for the tracker
            enable_caching: Whether to enable conversion caching
        """
        super().__init__(conf_threshold)

        self.enable_supervision = enable_supervision and SUPERVISION_AVAILABLE
        self.enable_tracking = enable_tracking and SUPERVISION_AVAILABLE

        # Initialize supervision converter
        self.converter = SupervisionConverter(enable_caching=enable_caching) if self.enable_supervision else None

        # Initialize tracker
        self.tracker = None
        if self.enable_tracking:
            tracker_config = tracker_config or {}
            self.tracker = SupervisionTracker(**tracker_config)

        # Initialize supervision annotators
        self.box_annotator = None
        self.label_annotator = None
        if self.enable_supervision:
            try:
                self.box_annotator = sv.BoxAnnotator()
                self.label_annotator = sv.LabelAnnotator()
            except Exception as e:
                logger.warning(f"Failed to initialize supervision annotators: {e}")
                self.enable_supervision = False

        logger.info(f"EnhancedFrameProcessor initialized (supervision={'enabled' if self.enable_supervision else 'disabled'}, "
                   f"tracking={'enabled' if self.enable_tracking else 'disabled'})")

    def process(self, pkt: FramePacket) -> np.ndarray:
        """
        Process frame packet with automatic supervision integration.

        This method automatically uses supervision format and visualization if enabled,
        otherwise falls back to the original processing method.

        Args:
            pkt: FramePacket containing frame and model output

        Returns:
            Processed RGB frame with annotations
        """
        if self.enable_supervision:
            return self.process_with_supervision(pkt, source_format='auto')
        else:
            # Fallback to original processing
            detections = self.parse_detections(pkt.model_out, pkt.frame_rgb.shape[:2])
            return self.draw_boxes(pkt.frame_rgb, detections)

    def process_with_supervision(
        self,
        pkt: FramePacket,
        source_format: str = 'auto',
        class_names: Optional[List[str]] = None
    ) -> np.ndarray:
        """
        Process frame packet using supervision format and visualization.

        Args:
            pkt: FramePacket containing frame and model output
            source_format: Source format of detections
            class_names: Optional class names for detections

        Returns:
            Processed RGB frame with professional annotations
        """
        if not self.enable_supervision:
            # Fallback to original processing
            return self.process(pkt)

        try:
            h, w = pkt.frame_rgb.shape[:2]

            # Convert detections to supervision format
            sv_detections = self.converter.convert_to_supervision(
                pkt.model_out,
                source_format=source_format,
                frame_shape=(h, w),
                class_names=class_names
            )

            if sv_detections is None or len(sv_detections) == 0:
                return pkt.frame_rgb.copy()

            # Apply tracking if enabled
            if self.tracker and self.tracker.is_available():
                sv_detections = self.tracker.update_with_detections(sv_detections)

            # Create professional annotations
            annotated_frame = pkt.frame_rgb.copy()

            if self.box_annotator:
                annotated_frame = self.box_annotator.annotate(
                    scene=annotated_frame,
                    detections=sv_detections
                )

            if self.label_annotator:
                # Create labels with tracking IDs if available
                labels = self._create_labels(sv_detections)
                annotated_frame = self.label_annotator.annotate(
                    scene=annotated_frame,
                    detections=sv_detections,
                    labels=labels
                )

            return annotated_frame

        except Exception as e:
            logger.error(f"Supervision processing failed: {e}")
            # Fallback to original processing
            return self.process(pkt)

    def _create_labels(self, sv_detections: "sv.Detections") -> List[str]:
        """Create labels for detections including tracking IDs."""
        labels = []

        for i in range(len(sv_detections)):
            label_parts = []

            # Add tracking ID if available
            if hasattr(sv_detections, 'tracker_id') and sv_detections.tracker_id is not None:
                if i < len(sv_detections.tracker_id):
                    label_parts.append(f"#{sv_detections.tracker_id[i]}")

            # Add class name if available
            if 'class_name' in sv_detections.data and i < len(sv_detections.data['class_name']):
                label_parts.append(str(sv_detections.data['class_name'][i]))

            # Add confidence score
            if i < len(sv_detections.confidence):
                label_parts.append(f"{sv_detections.confidence[i]:.2f}")

            labels.append(" ".join(label_parts) if label_parts else "detection")

        return labels

    def get_supervision_detections(
        self,
        pkt: FramePacket,
        source_format: str = 'auto',
        class_names: Optional[List[str]] = None
    ) -> Optional["sv.Detections"]:
        """
        Get detections in supervision format without visualization.

        Args:
            pkt: FramePacket containing frame and model output
            source_format: Source format of detections
            class_names: Optional class names for detections

        Returns:
            supervision.Detections object or None
        """
        if not self.enable_supervision:
            return None

        try:
            h, w = pkt.frame_rgb.shape[:2]

            sv_detections = self.converter.convert_to_supervision(
                pkt.model_out,
                source_format=source_format,
                frame_shape=(h, w),
                class_names=class_names
            )

            # Apply tracking if enabled
            if self.tracker and self.tracker.is_available() and sv_detections is not None:
                sv_detections = self.tracker.update_with_detections(sv_detections)

            return sv_detections

        except Exception as e:
            logger.error(f"Failed to get supervision detections: {e}")
            return None


# ----------------------------- Utility Functions and Factory Classes -----------------------------

class DetectionSourceFactory:
    """
    Factory class for creating detection sources and processors with supervision integration.

    This factory simplifies the creation of detection processing pipelines by providing
    pre-configured components that work seamlessly together.
    """

    @staticmethod
    def create_enhanced_processor(
        conf_threshold: float = 0.0,
        enable_supervision: bool = True,
        enable_tracking: bool = False,
        tracker_type: str = 'bytetrack',
        tracking_params: Optional[Dict[str, Any]] = None
    ) -> EnhancedFrameProcessor:
        """
        Create an enhanced frame processor with supervision and tracking capabilities.

        Args:
            conf_threshold: Confidence threshold for detections
            enable_supervision: Whether to enable supervision integration
            enable_tracking: Whether to enable object tracking
            tracker_type: Type of tracker to use
            tracking_params: Additional tracking parameters

        Returns:
            Configured EnhancedFrameProcessor instance
        """
        tracker_config = {'tracker_type': tracker_type}
        if tracking_params:
            tracker_config.update(tracking_params)

        return EnhancedFrameProcessor(
            conf_threshold=conf_threshold,
            enable_supervision=enable_supervision,
            enable_tracking=enable_tracking,
            tracker_config=tracker_config
        )

    @staticmethod
    def create_supervision_converter(enable_caching: bool = True) -> SupervisionConverter:
        """Create a supervision converter with optimal settings."""
        return SupervisionConverter(enable_caching=enable_caching)

    @staticmethod
    def create_tracker(
        tracker_type: str = 'bytetrack',
        sensitivity: str = 'medium'
    ) -> SupervisionTracker:
        """
        Create a tracker with predefined sensitivity settings.

        Args:
            tracker_type: Type of tracker ('bytetrack')
            sensitivity: Sensitivity level ('low', 'medium', 'high')

        Returns:
            Configured SupervisionTracker instance
        """
        # Predefined sensitivity configurations
        sensitivity_configs = {
            'low': {
                'track_activation_threshold': 0.5,
                'lost_track_buffer': 15,
                'minimum_matching_threshold': 0.9
            },
            'medium': {
                'track_activation_threshold': 0.25,
                'lost_track_buffer': 30,
                'minimum_matching_threshold': 0.8
            },
            'high': {
                'track_activation_threshold': 0.1,
                'lost_track_buffer': 50,
                'minimum_matching_threshold': 0.7
            }
        }

        config = sensitivity_configs.get(sensitivity, sensitivity_configs['medium'])
        config['tracker_type'] = tracker_type

        return SupervisionTracker(**config)


def create_hailo_showcase_processor(
    conf_threshold: float = 0.0,
    enable_tracking: bool = False,
    tracking_sensitivity: str = 'medium'
) -> EnhancedFrameProcessor:
    """
    Convenience function to create a processor optimized for Hailo detections.

    Args:
        conf_threshold: Confidence threshold for detections
        enable_tracking: Whether to enable object tracking
        tracking_sensitivity: Tracking sensitivity ('low', 'medium', 'high')

    Returns:
        Configured processor for Hailo detection workflows
    """
    tracking_params = None
    if enable_tracking:
        sensitivity_configs = {
            'low': {'track_activation_threshold': 0.5, 'lost_track_buffer': 15},
            'medium': {'track_activation_threshold': 0.25, 'lost_track_buffer': 30},
            'high': {'track_activation_threshold': 0.1, 'lost_track_buffer': 50}
        }
        tracking_params = sensitivity_configs.get(tracking_sensitivity, sensitivity_configs['medium'])

    return DetectionSourceFactory.create_enhanced_processor(
        conf_threshold=conf_threshold,
        enable_supervision=True,
        enable_tracking=enable_tracking,
        tracking_params=tracking_params
    )


def convert_detections_to_supervision(
    detections: Any,
    source_format: str = 'hailo',
    frame_shape: Optional[Tuple[int, int]] = None,
    class_names: Optional[List[str]] = None
) -> Optional["sv.Detections"]:
    """
    Convenience function for converting detections to supervision format.

    Args:
        detections: Detection results in various formats
        source_format: Source format ('hailo', 'yolo', 'generic', 'auto')
        frame_shape: (height, width) for coordinate normalization
        class_names: Optional class names for detections

    Returns:
        supervision.Detections object or None
    """
    converter = SupervisionConverter()
    return converter.convert_to_supervision(
        detections=detections,
        source_format=source_format,
        frame_shape=frame_shape,
        class_names=class_names
    )


def create_tracking_workflow(
    model: InferenceModel,
    processor_class: type = EnhancedFrameProcessor,
    tracking_sensitivity: str = 'medium',
    conf_threshold: float = 0.25
) -> Tuple[InferenceModel, EnhancedFrameProcessor]:
    """
    Create a complete tracking workflow with model and processor.

    Args:
        model: Inference model instance
        processor_class: Processor class to use
        tracking_sensitivity: Tracking sensitivity level
        conf_threshold: Detection confidence threshold

    Returns:
        Tuple of (model, processor) ready for tracking workflow
    """
    processor = processor_class(
        conf_threshold=conf_threshold,
        enable_supervision=True,
        enable_tracking=True,
        tracker_config={'tracker_type': 'bytetrack'}
    )

    return model, processor


# ----------------------------- Concrete helpers / simple implementations -----------------------------

class VideoFileSource(FrameSource):
    """Production-ready video file source that emulates live camera behavior.

    Features:
    - Real-time frame pacing to match video FPS
    - Robust error handling and recovery
    - Resource management with context manager support
    - Thread-safe operations
    - Comprehensive logging and monitoring
    - Graceful degradation and fallback mechanisms
    - Loop playback support for continuous operation
    - Frame dropping/buffering strategies

    Args:
        path: Path to video file
        resize: Optional (width, height) tuple for frame resizing
        realtime: If True, pace frame delivery to match video FPS
        speed: Playback speed multiplier (1.0 = normal speed)
        loop: If True, restart video when it reaches the end
        max_retries: Maximum number of retry attempts for failed operations
        buffer_size: Internal frame buffer size for smooth delivery
        drop_frames: If True, drop frames when consumer is slow
    """

    def __init__(
        self,
        path: str,
        resize: Optional[Tuple[int, int]] = None,
        realtime: bool = False,
        speed: float = 1.0,
        loop: bool = False,
        max_retries: int = 3,
        buffer_size: int = 5,
        drop_frames: bool = True
    ):
        # Input validation
        self._validate_inputs(path, resize, speed, max_retries, buffer_size)

        # Core configuration
        self.path = path
        self.resize = resize
        self.realtime = bool(realtime)
        self.speed = max(0.1, min(10.0, float(speed)))  # Clamp speed to reasonable range
        self.loop = bool(loop)
        self.max_retries = max(1, int(max_retries))
        self.buffer_size = max(1, int(buffer_size))
        self.drop_frames = bool(drop_frames)

        # Internal state
        self._cap: Optional[cv2.VideoCapture] = None
        self._stopped = True
        self._frame_interval: Optional[float] = None
        self._next_frame_time: Optional[float] = None
        self._lock = threading.RLock()  # Thread safety
        self._eof_reached = False  # Track end-of-file state

        # Video properties (cached after opening)
        self._fps: Optional[float] = None
        self._frame_count: Optional[int] = None
        self._current_frame: int = 0

        # Error handling and monitoring
        self._retry_count = 0
        self._error_count = 0
        self._frames_read = 0
        self._start_time: Optional[float] = None

        # Frame buffer for smooth delivery
        self._frame_buffer: queue.Queue = queue.Queue(maxsize=self.buffer_size)
        self._buffer_thread: Optional[threading.Thread] = None
        self._buffer_stop_event = threading.Event()

    def _validate_inputs(self, path: str, resize: Optional[Tuple[int, int]],
                        speed: float, max_retries: int, buffer_size: int) -> None:
        """Validate constructor inputs."""
        if not isinstance(path, str) or not path.strip():
            raise ValueError("Path must be a non-empty string")

        if resize is not None:
            if not isinstance(resize, (tuple, list)) or len(resize) != 2:
                raise ValueError("Resize must be a tuple of (width, height)")
            if not all(isinstance(x, int) and x > 0 for x in resize):
                raise ValueError("Resize dimensions must be positive integers")

        if not isinstance(speed, (int, float)) or speed <= 0:
            raise ValueError("Speed must be a positive number")

        if not isinstance(max_retries, int) or max_retries < 1:
            raise ValueError("Max retries must be a positive integer")

        if not isinstance(buffer_size, int) or buffer_size < 1:
            raise ValueError("Buffer size must be a positive integer")

    def __enter__(self):
        """Context manager entry."""
        self.start()
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        """Context manager exit."""
        self.stop()
        return False  # Don't suppress exceptions

    @property
    def is_opened(self) -> bool:
        """Check if video source is currently opened."""
        with self._lock:
            return self._cap is not None and self._cap.isOpened()

    @property
    def fps(self) -> Optional[float]:
        """Get video FPS."""
        return self._fps

    @property
    def frame_count(self) -> Optional[int]:
        """Get total frame count."""
        return self._frame_count

    @property
    def current_frame(self) -> int:
        """Get current frame number."""
        return self._current_frame

    @property
    def progress(self) -> float:
        """Get playback progress (0.0 to 1.0)."""
        if self._frame_count and self._frame_count > 0:
            return min(1.0, self._current_frame / self._frame_count)
        return 0.0

    def get_stats(self) -> Dict[str, Any]:
        """Get operational statistics."""
        with self._lock:
            elapsed = time.time() - self._start_time if self._start_time else 0
            effective_fps = self._frames_read / elapsed if elapsed > 0 else 0

            return {
                'frames_read': self._frames_read,
                'current_frame': self._current_frame,
                'progress': self.progress,
                'error_count': self._error_count,
                'retry_count': self._retry_count,
                'effective_fps': effective_fps,
                'buffer_size': self._frame_buffer.qsize(),
                'is_opened': self.is_opened,
                'elapsed_time': elapsed
            }

    def start(self) -> None:
        """Start the video source with robust error handling."""
        with self._lock:
            if not self._stopped:
                logger.warning("VideoFileSource already started")
                return

            self._start_time = time.time()
            self._stopped = False
            self._retry_count = 0
            self._eof_reached = False  # Reset EOF flag

            # Attempt to open video with retries
            for attempt in range(self.max_retries):
                try:
                    self._open_video()
                    break
                except Exception as e:
                    self._retry_count += 1
                    logger.warning(f"Failed to open video (attempt {attempt + 1}/{self.max_retries}): {e}")
                    if attempt == self.max_retries - 1:
                        raise RuntimeError(f"Failed to open video file after {self.max_retries} attempts: {self.path}")
                    time.sleep(0.5 * (attempt + 1))  # Exponential backoff

            # Start buffer thread if using buffered mode
            if self.buffer_size > 1:
                self._buffer_stop_event.clear()
                self._buffer_thread = threading.Thread(target=self._buffer_loop, daemon=True)
                self._buffer_thread.start()

            logger.info(
                "VideoFileSource started: %s (realtime=%s, fps=%.3f, loop=%s, buffer_size=%d)",
                self.path, self.realtime, self._fps, self.loop, self.buffer_size
            )

    def _open_video(self) -> None:
        """Open video file and cache properties."""
        import os

        # Validate file exists
        if not os.path.isfile(self.path):
            raise FileNotFoundError(f"Video file not found: {self.path}")

        # Open video capture
        self._cap = cv2.VideoCapture(self.path)
        if not self._cap.isOpened():
            raise RuntimeError(f"OpenCV failed to open video file: {self.path}")

        # Cache video properties
        self._fps = self._cap.get(cv2.CAP_PROP_FPS) or 30.0
        if self._fps <= 1e-3:
            self._fps = 30.0
            logger.warning("Invalid FPS detected, using default 30.0")

        self._frame_count = int(self._cap.get(cv2.CAP_PROP_FRAME_COUNT))
        if self._frame_count <= 0:
            logger.warning("Could not determine frame count")

        # Calculate frame interval for real-time pacing
        effective_fps = self._fps * self.speed
        self._frame_interval = 1.0 / effective_fps
        self._next_frame_time = None
        self._current_frame = 0

        logger.debug(
            "Video opened: fps=%.3f, frames=%d, duration=%.2fs",
            self._fps, self._frame_count,
            self._frame_count / self._fps if self._fps > 0 else 0
        )

    def _buffer_loop(self) -> None:
        """Background thread for frame buffering."""
        logger.debug("Frame buffer thread started")

        while not self._buffer_stop_event.is_set():
            try:
                if self._frame_buffer.qsize() < self.buffer_size:
                    frame = self._read_frame_internal()
                    if frame is not None:
                        try:
                            self._frame_buffer.put_nowait(frame)
                        except queue.Full:
                            if self.drop_frames:
                                # Drop oldest frame
                                try:
                                    self._frame_buffer.get_nowait()
                                    self._frame_buffer.put_nowait(frame)
                                except queue.Empty:
                                    pass
                    else:
                        # End of video
                        if self.loop:
                            self._restart_video()
                        else:
                            # Signal EOF and stop buffering
                            with self._lock:
                                self._eof_reached = True
                            logger.debug("EOF reached, buffer thread stopping")
                            break
                else:
                    time.sleep(0.001)  # Small sleep when buffer is full

            except Exception as e:
                logger.error(f"Buffer thread error: {e}")
                self._error_count += 1
                time.sleep(0.1)

        logger.debug("Frame buffer thread stopped")

    def _restart_video(self) -> None:
        """Restart video from beginning for loop playback."""
        try:
            if self._cap and self._cap.isOpened():
                self._cap.set(cv2.CAP_PROP_POS_FRAMES, 0)
                self._current_frame = 0
                self._next_frame_time = None
                with self._lock:
                    self._eof_reached = False  # Reset EOF flag on restart
                logger.debug("Video restarted for loop playback")
        except Exception as e:
            logger.error(f"Failed to restart video: {e}")
            self._error_count += 1

    def read(self) -> Optional[np.ndarray]:
        """Read next frame with robust error handling and real-time pacing."""
        if self._stopped:
            return None

        # Check if EOF has been reached
        with self._lock:
            if self._eof_reached and self._frame_buffer.empty():
                logger.debug("EOF reached and buffer empty, returning None")
                return None

        try:
            # Use buffered read if buffer thread is active
            if self._buffer_thread and self._buffer_thread.is_alive():
                return self._read_from_buffer()
            else:
                return self._read_frame_direct()

        except Exception as e:
            logger.error(f"Frame read error: {e}")
            self._error_count += 1
            return None

    def _read_from_buffer(self) -> Optional[np.ndarray]:
        """Read frame from internal buffer."""
        try:
            frame = self._frame_buffer.get(timeout=0.1)
            self._apply_realtime_pacing()
            return frame
        except queue.Empty:
            # Buffer empty, check if EOF reached
            with self._lock:
                if self._eof_reached:
                    logger.debug("Buffer empty and EOF reached")
                    return None
            # Buffer empty but not EOF, try direct read as fallback
            return self._read_frame_direct()

    def _read_frame_direct(self) -> Optional[np.ndarray]:
        """Read frame directly from video source."""
        frame = self._read_frame_internal()
        if frame is not None:
            self._apply_realtime_pacing()
        elif self.loop:
            # Try to restart for loop playback
            self._restart_video()
            frame = self._read_frame_internal()
            if frame is not None:
                self._apply_realtime_pacing()
        else:
            # EOF reached and not looping
            with self._lock:
                self._eof_reached = True
            logger.debug("EOF reached in direct read")

        return frame

    def _read_frame_internal(self) -> Optional[np.ndarray]:
        """Internal frame reading with error handling."""
        with self._lock:
            if not self.is_opened:
                return None

            try:
                ret, frame_bgr = self._cap.read()
                if not ret:
                    return None

                # Update frame counter
                self._current_frame += 1
                self._frames_read += 1

                # Apply resize if specified
                if self.resize is not None:
                    frame_bgr = cv2.resize(frame_bgr, self.resize)

                # Convert to RGB
                frame_rgb = cv2.cvtColor(frame_bgr, cv2.COLOR_BGR2RGB)
                return frame_rgb

            except Exception as e:
                logger.error(f"Internal frame read error: {e}")
                self._error_count += 1
                return None

    def _apply_realtime_pacing(self) -> None:
        """Apply real-time frame pacing to match video FPS."""
        if not self.realtime or self._frame_interval is None:
            return

        now = time.monotonic()
        if self._next_frame_time is None:
            # First frame: schedule next time
            self._next_frame_time = now + self._frame_interval
        else:
            # Sleep until scheduled time if we're early
            to_wait = self._next_frame_time - now
            if to_wait > 0:
                time.sleep(to_wait)
                # Schedule next frame relative to previous schedule
                self._next_frame_time += self._frame_interval
            else:
                # We're late; advance next_frame_time to avoid drift
                self._next_frame_time = now + self._frame_interval

    def stop(self) -> None:
        """Stop the video source and clean up resources."""
        with self._lock:
            if self._stopped:
                return

            self._stopped = True

            # Stop buffer thread
            if self._buffer_thread:
                self._buffer_stop_event.set()
                try:
                    self._buffer_thread.join(timeout=2.0)
                except Exception:
                    pass
                self._buffer_thread = None

            # Clear buffer
            while not self._frame_buffer.empty():
                try:
                    self._frame_buffer.get_nowait()
                except queue.Empty:
                    break

            # Release video capture
            if self._cap is not None:
                try:
                    self._cap.release()
                except Exception as e:
                    logger.warning(f"Error releasing video capture: {e}")
                finally:
                    self._cap = None

            # Log final statistics
            stats = self.get_stats()
            logger.info(
                "VideoFileSource stopped: frames_read=%d, errors=%d, effective_fps=%.2f",
                stats['frames_read'], stats['error_count'], stats['effective_fps']
            )

    def seek(self, frame_number: int) -> bool:
        """Seek to specific frame number."""
        with self._lock:
            if not self.is_opened or frame_number < 0:
                return False

            try:
                if self._frame_count and frame_number >= self._frame_count:
                    frame_number = self._frame_count - 1

                self._cap.set(cv2.CAP_PROP_POS_FRAMES, frame_number)
                self._current_frame = frame_number
                self._next_frame_time = None  # Reset timing
                return True

            except Exception as e:
                logger.error(f"Seek error: {e}")
                self._error_count += 1
                return False

    def seek_time(self, seconds: float) -> bool:
        """Seek to specific time position."""
        if self._fps and self._fps > 0:
            frame_number = int(seconds * self._fps)
            return self.seek(frame_number)
        return False

    def reset(self) -> bool:
        """Reset video to beginning."""
        return self.seek(0)

    def set_speed(self, speed: float) -> None:
        """Change playback speed dynamically."""
        with self._lock:
            self.speed = max(0.1, min(10.0, float(speed)))
            if self._fps:
                effective_fps = self._fps * self.speed
                self._frame_interval = 1.0 / effective_fps
                self._next_frame_time = None  # Reset timing
                logger.debug(f"Speed changed to {self.speed}x (effective fps: {effective_fps:.2f})")

    def get_duration(self) -> Optional[float]:
        """Get video duration in seconds."""
        if self._fps and self._frame_count and self._fps > 0:
            return self._frame_count / self._fps
        return None

    def get_remaining_time(self) -> Optional[float]:
        """Get remaining playback time in seconds."""
        duration = self.get_duration()
        if duration and self._fps and self._fps > 0:
            current_time = self._current_frame / self._fps
            return max(0.0, duration - current_time)
        return None

    def is_live_like(self) -> bool:
        """Check if source is configured to behave like a live camera."""
        return self.realtime and not self.loop

    def __str__(self) -> str:
        """String representation."""
        return f"VideoFileSource(path='{self.path}', realtime={self.realtime}, loop={self.loop})"

    def __repr__(self) -> str:
        """Detailed string representation."""
        return (
            f"VideoFileSource(path='{self.path}', resize={self.resize}, "
            f"realtime={self.realtime}, speed={self.speed}, loop={self.loop}, "
            f"buffer_size={self.buffer_size}, fps={self._fps})"
        )



class DummyModel(InferenceModel):
    """A tiny model that returns synthetic detections for testing/showcase."""

    def infer(self, frame_rgb: np.ndarray) -> Any:
        h, w = frame_rgb.shape[:2]
        # produce two boxes: center and top-left
        cx, cy = w // 2, h // 2
        box1 = [cx - w * 0.15, cy - h * 0.15, cx + w * 0.15, cy + h * 0.15]
        box2 = [w * 0.1, h * 0.1, w * 0.3, h * 0.3]
        return [
            {"bbox": box1, "score": 0.9, "label": "dummy_center"},
            {"bbox": box2, "score": 0.6, "label": "dummy_tl"},
        ]


class OpenCVFileSink(FrameSink):
    """Robust file sink for Raspberry Pi using GStreamer for MP4 (H.264) encoding.

    This class fixes a common problem where the output file does not respect the
    requested FPS by:
      - providing explicit caps (including framerate) to the GStreamer pipeline
      - adding `videorate` and `h264parse` where needed
      - and applying a time-based throttle to writes as a fallback

    Note: GStreamer must be available and built with the necessary plugins for
    H.264 encoding for the GStreamer path to work. If the GStreamer writer fails
    we fall back to a simple XVID AVI writer which respects the FPS argument.
    """

    def __init__(self, path: str, fps: float = 15.0):
        self.path = path
        self.fps = float(fps)
        self.width = None
        self.height = None
        self._q: queue.Queue = queue.Queue(maxsize=512)
        self._stop_ev = threading.Event()
        self._thread: Optional[threading.Thread] = None
        self._writer: Optional[cv2.VideoWriter] = None
        self._last_write_ts: Optional[float] = None
        # precompute frame interval
        self._frame_interval = 1.0 / max(1e-6, float(self.fps))

            # write counters & timing for monitoring
        self._written_count = 0
        self._written_lock = threading.Lock()
        self._writer_start_ts: Optional[float] = None
    def _fps_to_gst_fraction(self) -> str:
        # Convert float fps to a rational string for GStreamer caps, e.g. "30/1" or "30000/1001"
        try:
            from fractions import Fraction
            f = Fraction(self.fps).limit_denominator(1001)
            return f"{f.numerator}/{f.denominator}"
        except Exception:
            return f"{int(round(self.fps))}/1"

    def start(self):
        self._stop_ev.clear()
        self._thread = threading.Thread(target=self._run, daemon=True)
        self._thread.start()
        logger.info("OpenCVFileSink started: %s", self.path)

    def put(self, frame_rgb: np.ndarray):
        # Lazy init writer on first frame
        if self._writer is None:
            h, w = frame_rgb.shape[:2]
            self.width, self.height = w, h
            # Build GStreamer pipeline with explicit caps for framerate and resolution
            gst_fps = self._fps_to_gst_fraction()
            gst_str = (
                f"appsrc is-live=true format=time do-timestamp=true ! "
                f"video/x-raw,format=BGR,framerate={gst_fps},width={w},height={h} ! "
                f"videoconvert ! videorate ! x264enc tune=zerolatency bitrate=500 speed-preset=superfast ! "
                f"h264parse ! mp4mux ! filesink location={self.path}"
            )
            try:
                self._writer = cv2.VideoWriter(
                    gst_str,
                    cv2.CAP_GSTREAMER,
                    0,
                    float(self.fps),
                    (w, h),
                )
            except Exception:
                self._writer = None

            if self._writer is None or not self._writer.isOpened():
                # fallback to XVID AVI
                logger.warning("GStreamer H.264 failed or unavailable, falling back to XVID AVI")
                self.path = self.path.rsplit(".", 1)[0] + ".avi"
                fourcc = cv2.VideoWriter_fourcc(*"XVID")
                self._writer = cv2.VideoWriter(self.path, fourcc, float(self.fps), (w, h))

            if not self._writer.isOpened():
                raise RuntimeError(f"Failed to create video writer for {self.path}")

            # reset last write timestamp so the time throttle starts fresh
            self._last_write_ts = None
            try:
                # writer accepted: record start time for effective FPS computation
                self._writer_start_ts = time.time()
                with self._written_lock:
                    self._written_count = 0
            except Exception:
                pass

        try:
            self._q.put_nowait(frame_rgb)
        except queue.Full:
            _ = self._q.get_nowait()
            self._q.put_nowait(frame_rgb)

    def _run(self):
        while not self._stop_ev.is_set() or not self._q.empty():
            try:
                frame = self._q.get(timeout=0.5)
            except queue.Empty:
                continue
            try:
                # resize if needed
                if frame.shape[:2] != (self.height, self.width):
                    frame = cv2.resize(frame, (self.width, self.height))
                bgr = cv2.cvtColor(frame, cv2.COLOR_RGB2BGR)

                # Time-based write throttle to make sure we don't write frames faster
                # than the requested FPS. This helps when the input/source is producing
                # frames faster than the desired output FPS or when the writer does
                # not enforce timing.
                now = time.time()
                if self._last_write_ts is not None:
                    elapsed = now - self._last_write_ts
                    if elapsed < self._frame_interval:
                        # Sleep only for the remaining time slice
                        to_wait = self._frame_interval - elapsed
                        # If to_wait is large, cap it to avoid blocking for long durations
                        if to_wait > 0:
                            time.sleep(to_wait)

                # Write the frame
                self._writer.write(bgr)
                self._last_write_ts = time.time()
                # update write counters for monitoring
                try:
                    with self._written_lock:
                        self._written_count += 1
                        wc = int(self._written_count)
                    if wc % 30 == 0:
                        if self._writer_start_ts:
                            elapsed = time.time() - self._writer_start_ts
                            if elapsed > 0.001:
                                fps_eff = float(wc) / float(elapsed)
                                logger.info("Sink wrote %d frames (effective fps: %.2f) to %s", wc, fps_eff, self.path)
                except Exception:
                    pass

            except Exception as e:
                logger.error("Sink write error: %s", e)
            finally:
                try:
                    self._q.task_done()
                except Exception:
                    pass
        if self._writer:
            try:
                self._writer.release()
            except Exception:
                pass
        logger.info("OpenCVFileSink finished writing")

    def stop(self):
        self._stop_ev.set()
        if self._thread is not None:
            self._thread.join(timeout=10.0)
        # Ensure writer is properly closed
        if self._writer is not None:
            try:
                self._writer.release()
                self._writer = None
            except Exception as e:
                logger.debug(f"Writer release error: {e}")
        logger.info("OpenCVFileSink stopped")


class Picamera2Source(FrameSource):
    """FrameSource using Picamera2 on Raspberry Pi.
    Outputs RGB frames (HxWx3 uint8).
    """

    def __init__(self, size=(1280, 720), format="RGB888"):
        from picamera2 import Picamera2

        self.size = size
        self.format = format
        self.picam2 = Picamera2()
        self._stopped = True

        # Configure camera
        config = self.picam2.create_preview_configuration(
            main={"size": self.size, "format": self.format}
        )
        self.picam2.configure(config)

    def start(self):
        self.picam2.start()
        self._stopped = False
        logger.info("Picamera2Source started: size=%s format=%s", self.size, self.format)

    def read(self) -> Optional[np.ndarray]:
        if self._stopped:
            return None
        try:
            frame = self.picam2.capture_array()
            # Already in RGB888 format
            return frame
        except Exception as e:
            logger.error("Picamera2 capture error: %s", e)
            return None

    def stop(self):
        self._stopped = True
        try:
            if hasattr(self, 'picam2') and self.picam2 is not None:
                self.picam2.stop()
                # Give camera time to stop cleanly
                import time
                time.sleep(0.1)
        except Exception as e:
            logger.debug(f"Picamera2 stop error: {e}")
        try:
            if hasattr(self, 'picam2') and self.picam2 is not None:
                self.picam2.close()
                self.picam2 = None
        except Exception as e:
            logger.debug(f"Picamera2 close error: {e}")
        logger.info("Picamera2Source stopped")


class DegirumRetinaFaceModel(InferenceModel):
    """
    Degirum RetinaFace model adapter for the showcase framework.
    Requires `degirum` package and a local model zoo path.

    Usage:
        model = DegirumRetinaFaceModel(
            model_name="retinaface_mobilenet--736x1280_quant_hailort_hailo8l_1",
            zoo_path="~/degirum-zoo"
        )
    """

    def __init__(self, model_name: str, zoo_path: Optional[str] = None):
        try:
            import degirum as dg
        except ImportError:
            raise RuntimeError(
                "The 'degirum' package is not installed. Install it or use DummyModel for testing."
            )
        import os

        self.dg = dg
        self.model_name = model_name
        self.zoo_path = os.path.expanduser(zoo_path) if zoo_path else None
        self.zoo = None
        self.model = None

    def load(self) -> None:
        logger.info("Connecting to Degirum local zoo: %s", self.zoo_path)
        self.zoo = self.dg.connect(self.dg.LOCAL, self.zoo_path)
        self.model = self.zoo.load_model(self.model_name)
        try:
            # Some runtimes accept this hint for correct color ordering
            self.model.input_numpy_colorspace = "RGB"
        except Exception:
            pass
        logger.info("Degirum model loaded: %s", self.model_name)

    def infer(self, frame_rgb: np.ndarray) -> Any:
        if self.model is None:
            self.load()
        return self.model(frame_rgb)

    def warmup(self, frame_rgb: np.ndarray) -> None:
        """Optional warmup to prime the accelerator."""
        if self.model is None:
            self.load()
        try:
            _ = self.model(frame_rgb)
        except Exception as e:
            logger.warning("Degirum warmup failed: %s", e)



# ----------------------------- Pipeline -----------------------------

class BufferedPipeline:
    """Capture -> Infer -> Process -> Sink pipeline with bounded queues.

    Attributes exposed for compatibility with existing scripts: preview_q (frames for display)
    """

    def __init__(
        self,
        source: FrameSource,
        model: InferenceModel,
        processor: FrameProcessor,
        sink: Optional[FrameSink] = None,
        input_q_size: int = 8,
        proc_q_size: int = 8,
        save_q_size: int = 128,
        preview_q_size: int = 1,
        infer_workers: int = 1,
    ) -> None:
        self.source = source
        self.model = model
        self.processor = processor
        self.sink = sink

        self.input_q: queue.Queue = queue.Queue(maxsize=max(1, int(input_q_size)))
        self.proc_q: queue.Queue = queue.Queue(maxsize=max(1, int(proc_q_size)))
        self.save_q: queue.Queue = queue.Queue(maxsize=max(1, int(save_q_size)))
        self.preview_q: queue.Queue = queue.Queue(maxsize=max(1, int(preview_q_size)))

        self._threads: List[threading.Thread] = []
        self._stop_ev = threading.Event()
        self._frame_idx = 0
        self._infer_workers = max(1, int(infer_workers))


        # Counters for monitoring progress
        self._counter_lock = threading.Lock()
        self._captured_count = 0
        self._inferred_count = 0
        self._processed_count = 0
        self._sink_handed_count = 0
        self._monitor_thread: Optional[threading.Thread] = None
    # ---------------- lifecycle ----------------
    def start(self) -> None:
        logger.info("Starting pipeline")
        self.source.start()
        try:
            # attempt warmup to initialize weights/hardware
            dummy = self.source.read()
            if dummy is not None:
                self.model.warmup(dummy)
                # push dummy back to source by storing it for first read loop? We can't easily push back,
                # so we continue normally - this read consumes one frame from source. It's acceptable.
        except Exception:
            pass

        # start sink
        if self.sink is not None:
            # estimate size from first frame if possible
            self.sink.start()

        # capture thread
        t_cap = threading.Thread(target=self._capture_loop, daemon=True)
        t_cap.start()
        self._threads.append(t_cap)

        # inference threads
        for _ in range(self._infer_workers):
            t_inf = threading.Thread(target=self._inference_loop, daemon=True)
            t_inf.start()
            self._threads.append(t_inf)

        # processing thread
        t_proc = threading.Thread(target=self._processing_loop, daemon=True)
        t_proc.start()
        self._threads.append(t_proc)

        # sink thread
        if self.sink is not None:
            t_sink = threading.Thread(target=self._sink_loop, daemon=True)
            t_sink.start()
            self._threads.append(t_sink)

        # monitoring thread (logs counts & queue sizes periodically)
        try:
            self._monitor_thread = threading.Thread(target=self._monitor_loop, daemon=True)
            self._monitor_thread.start()
            self._threads.append(self._monitor_thread)
        except Exception:
            pass

    def stop_capture(self) -> None:
        logger.info("Stopping capture (pipeline)")
        self._stop_ev.set()
        try:
            self.source.stop()
        except Exception:
            pass

    def stop_all(self) -> None:
        logger.info("Stopping all pipeline threads")
        self._stop_ev.set()
        try:
            self.source.stop()
        except Exception:
            pass
        try:
            if self.sink is not None:
                self.sink.stop()
        except Exception:
            pass

    def join(self, timeout: Optional[float] = None) -> None:
        # wait for threads to finish
        start = time.time()
        for t in self._threads:
            remaining = None
            if timeout is not None:
                elapsed = time.time() - start
                remaining = max(0.0, timeout - elapsed)
            t.join(timeout=remaining)

    def _monitor_loop(self):
        """Background monitor that logs pipeline progress periodically."""
        interval = 5.0
        try:
            while not self._stop_ev.is_set():
                time.sleep(interval)
                try:
                    with self._counter_lock:
                        cap = int(self._captured_count)
                        inf = int(self._inferred_count)
                        proc = int(self._processed_count)
                        handed = int(self._sink_handed_count)
                except Exception:
                    cap = inf = proc = handed = 0
                logger.info(
                    "Pipeline progress: captured=%d inferred=%d processed=%d sink_handed=%d | queues: input=%d proc=%d save=%d preview=%d",
                    cap, inf, proc, handed, self.input_q.qsize(), self.proc_q.qsize(), self.save_q.qsize(), self.preview_q.qsize(),
                )
            logger.info("Monitor thread exiting")
        except Exception as e:
            logger.exception("Monitor loop error: %s", e)

    # ---------------- core loops ----------------
    def _capture_loop(self) -> None:
        logger.info("Capture loop started")
        while not self._stop_ev.is_set():
            try:
                frame_rgb = self.source.read()
            except Exception as e:
                logger.error("Source read error: %s", e)
                break
            if frame_rgb is None:
                # EOF or no frame - break the loop
                logger.info("No more frames from source")
                self._stop_ev.set()
                break

            # push to input queue; if full drop oldest to keep low-latency preview
            try:
                # increment captured counter
                try:
                    with self._counter_lock:
                        self._captured_count += 1
                except Exception:
                    pass
                self.input_q.put_nowait((self._frame_idx, frame_rgb, time.time()))
            except queue.Full:
                try:
                    _ = self.input_q.get_nowait()
                    self.input_q.put_nowait((self._frame_idx, frame_rgb, time.time()))
                except Exception:
                    pass

            self._frame_idx += 1

        logger.info("Capture loop finished")

    def _inference_loop(self) -> None:
        logger.info("Inference worker started")
        while not self._stop_ev.is_set() or not self.input_q.empty():
            try:
                idx, frame_rgb, ts = self.input_q.get(timeout=0.5)
            except queue.Empty:
                continue
            try:
                model_out = self.model.infer(frame_rgb)
            except Exception as e:
                logger.exception("Model inference error: %s", e)
                model_out = None

            pkt = FramePacket(frame_rgb=frame_rgb, model_out=model_out, timestamp=ts, frame_idx=idx, meta={})
            # increment inferred counter
            try:
                with self._counter_lock:
                    self._inferred_count += 1
            except Exception:
                pass
            # push to proc queue (drop oldest if full)
            try:
                self.proc_q.put_nowait(pkt)
            except queue.Full:
                try:
                    _ = self.proc_q.get_nowait()
                    self.proc_q.put_nowait(pkt)
                except Exception:
                    pass
            finally:
                try:
                    self.input_q.task_done()
                except Exception:
                    pass

        logger.info("Inference worker finished")

    def _processing_loop(self) -> None:
        logger.info("Processing worker started")
        while not self._stop_ev.is_set() or not self.proc_q.empty():
            try:
                pkt = self.proc_q.get(timeout=0.5)
            except queue.Empty:
                continue
            try:
                processed_rgb = self.processor.process(pkt)
            except Exception as e:
                logger.exception("Processor error: %s", e)
                processed_rgb = pkt.frame_rgb

            # increment processed counter
            try:
                with self._counter_lock:
                    self._processed_count += 1
            except Exception:
                pass

            # Put frame for preview (drop oldest)
            try:
                self.preview_q.put_nowait(processed_rgb)
            except queue.Full:
                try:
                    _ = self.preview_q.get_nowait()
                    self.preview_q.put_nowait(processed_rgb)
                except Exception:
                    pass

            # Put frame to save queue if sink present
            if self.sink is not None:
                try:
                    self.save_q.put_nowait(processed_rgb)
                except queue.Full:
                    # For save we prefer oldest drop policy too - drop oldest.
                    try:
                        _ = self.save_q.get_nowait()
                        self.save_q.put_nowait(processed_rgb)
                    except Exception:
                        pass

            try:
                self.proc_q.task_done()
            except Exception:
                pass

        logger.info("Processing worker finished")

    def _sink_loop(self) -> None:
        logger.info("Sink worker started")
        while not self._stop_ev.is_set() or not self.save_q.empty():
            try:
                frame_rgb = self.save_q.get(timeout=0.5)
            except queue.Empty:
                continue
            try:
                # increment counter for frames handed to sink
                try:
                    with self._counter_lock:
                        self._sink_handed_count += 1
                except Exception:
                    pass
                self.sink.put(frame_rgb)
            except Exception as e:
                logger.exception("Sink put error: %s", e)
            finally:
                try:
                    self.save_q.task_done()
                except Exception:
                    pass
        logger.info("Sink worker finished")


# ----------------------------- Example: run_picam_showcase.py (business-only) -----------------------------
# The following is an example usage that should live in your showcase script.
# It demonstrates a minimal 'business-only' scenario: draw ALL detections for any model.

if __name__ == "__main__":
    import argparse

    ap = argparse.ArgumentParser(description="Run a generic detection showcase (business-only scenario)")
    ap.add_argument("--video-file", type=str, default=None, help="Optional path to video file (instead of camera)")
    ap.add_argument("--out", type=str, default="output_new.mp4", help="Where to save processed video")
    ap.add_argument("--conf", type=float, default=0.0, help="Minimum confidence to show")
    ap.add_argument("--input-q-size", type=int, default=8)
    ap.add_argument("--proc-q-size", type=int, default=8)
    ap.add_argument("--save-q-size", type=int, default=64)
    ap.add_argument("--realtime", action="store_true", help="pace input reads to file FPS (emulate live camera)")
    ap.add_argument("--preview-q-size", type=int, default=1)
    ap.add_argument("--fps", type=float, default=15.0)
    ap.add_argument("--debug", action="store_true")
    ap.add_argument("--headless", action="store_true", help="Run without preview window (headless)")
    args = ap.parse_args()

    if args.debug:
        logging.getLogger().setLevel(logging.DEBUG)

    # Choose source
    if args.video_file:
        source = VideoFileSource(args.video_file,realtime=args.realtime)
    else:
        try:
            source = Picamera2Source(size=(1280, 720))
        except ImportError:
            logger.error("Picamera2 not available. Please install it or provide --video-file.")
            exit(1)

    # Choose model (try to use your DeGirum model if available, else DummyModel)
    try:
        model = DegirumRetinaFaceModel(
            #model_name="yolov8n_relu6_car--640x640_quant_hailort_multidevice_1",
            #model_name="hand_landmark_lite--224x224_quant_hailort_hailo8l_1",
            model_name="retinaface_mobilenet--736x1280_quant_hailort_hailo8l_1",
            zoo_path="~/degirum-zoo"  # or None if using remote connection
            # connection_str="hostname_or_ip:port"  # if using remote zoo
        )
    except Exception as e:
        logger.warning("Falling back to DummyModel: %s", e)
        model = DummyModel()


    # Business-only scenario class (developer writes only this)
    class DrawAllDetectionsScenario(FrameProcessor):
        def __init__(self, conf_threshold: float = 0.0):
            super().__init__(conf_threshold=conf_threshold)

        def process(self, pkt: FramePacket) -> np.ndarray:
            # parse detections using the robust helper
            h, w = pkt.frame_rgb.shape[:2]
            dets = self.parse_detections(pkt.model_out, (h, w))
            # example business filter: show only objects with label in allowlist (None => all)
            # allowlist = ("car", "person")
            # dets = [d for d in dets if (d.label in allowlist) or (d.label is None)]
            # draw boxes and return the frame
            return self.draw_boxes(pkt.frame_rgb, dets)

    processor = DrawAllDetectionsScenario(conf_threshold=args.conf)

    # sink - use first frame's size to create sink in start (we approximate using a test frame later)
    # for simplicity, pick conservative size: 1280x720 or derive from source
    sample_frame = None
    try:
        # attempt to start source and read a frame for sizing
        source.start()
        sample_frame = source.read()
    except Exception:
        pass

    if sample_frame is None:
        # fallback size
        sw, sh = 1280, 720
        sample_frame = np.zeros((sh, sw, 3), dtype=np.uint8)
    else:
        h, w = sample_frame.shape[:2]
        sw, sh = w, h

    sink = OpenCVFileSink(args.out, fps=args.fps)

    # Create pipeline
    pipeline = BufferedPipeline(
        source=source,
        model=model,
        processor=processor,
        sink=sink,
        input_q_size=args.input_q_size,
        proc_q_size=args.proc_q_size,
        save_q_size=args.save_q_size,
        preview_q_size=args.preview_q_size,
    )

    try:
        pipeline.start()
        if not args.headless:
            window = "Preview (press q to stop)"
            cv2.namedWindow(window, cv2.WINDOW_NORMAL)
        else:
            logger.info("Headless mode enabled: no preview window. Use Ctrl+C to stop or wait for source EOF.")

        while True:
            try:
                frame_rgb = pipeline.preview_q.get(timeout=0.5)
            except Exception:
                frame_rgb = None
            if frame_rgb is not None and not args.headless:
                cv2.imshow(window, cv2.cvtColor(frame_rgb, cv2.COLOR_RGB2BGR))
            # handle quit only in non-headless mode
            if not args.headless:
                if cv2.waitKey(1) & 0xFF == ord("q"):
                    pipeline.stop_capture()
                    break

            # Exit if pipeline is done and queues are drained
            if pipeline._stop_ev.is_set() and pipeline.preview_q.empty() and pipeline.save_q.empty():
                logger.info("Pipeline signaled stop and queues are empty; exiting main loop")
                break

            time.sleep(0.005)

        pipeline.join(timeout=120.0)

    except KeyboardInterrupt:
        pipeline.stop_all()
        pipeline.join(timeout=10.0)
    finally:
        try:
            if not args.headless:
                cv2.destroyAllWindows()
        except Exception:
            pass

# python showcase_framework.py  --video-file "/home/<USER>/jk/dev/hailotest/videos/people1_30s.mp4" --conf 0.5 --headless --realtime

# ----------------------------- Usage Examples and Integration Guide -----------------------------

"""
SUPERVISION INTEGRATION USAGE EXAMPLES

This section provides comprehensive examples of how to use the enhanced supervision
integration features in the showcase framework.

## Basic Usage Examples

### 1. Simple Hailo Detection with Supervision Visualization

```python
from showcase_framework import (
    VideoFileSource, DegirumRetinaFaceModel,
    create_hailo_showcase_processor, BufferedPipeline
)

# Create components
source = VideoFileSource("input.mp4")
model = DegirumRetinaFaceModel("retinaface_mobilenet--736x1280_quant_hailort_hailo8l_1")

# Create processor with supervision integration
processor = create_hailo_showcase_processor(
    conf_threshold=0.5,
    enable_tracking=True,
    tracking_sensitivity='medium'
)

# Create pipeline
pipeline = BufferedPipeline(source, model, processor)
pipeline.start()

# Process frames
try:
    while True:
        # Pipeline automatically handles conversion and tracking
        pass
except KeyboardInterrupt:
    pipeline.stop_all()
```

### 2. Custom Detection Processing with Supervision

```python
from showcase_framework import EnhancedFrameProcessor, SupervisionConverter

class CustomDetectionProcessor(EnhancedFrameProcessor):
    def process(self, pkt):
        # Use supervision integration for professional visualization
        if self.enable_supervision:
            return self.process_with_supervision(
                pkt,
                source_format='hailo',
                class_names=['person', 'face', 'vehicle']
            )
        else:
            # Fallback to original processing
            return super().process(pkt)

# Initialize with tracking
processor = CustomDetectionProcessor(
    conf_threshold=0.3,
    enable_supervision=True,
    enable_tracking=True,
    tracker_config={
        'track_activation_threshold': 0.25,
        'lost_track_buffer': 30,
        'minimum_matching_threshold': 0.8
    }
)
```

### 3. Manual Detection Conversion

```python
from showcase_framework import convert_detections_to_supervision

# Convert Hailo detections to supervision format
hailo_detections = [
    {"bbox": [100, 100, 200, 200], "score": 0.95, "label": "person"},
    {"bbox": [300, 150, 400, 250], "score": 0.87, "label": "face"}
]

sv_detections = convert_detections_to_supervision(
    detections=hailo_detections,
    source_format='hailo',
    frame_shape=(480, 640),  # height, width
    class_names=['person', 'face']
)

# Use with supervision annotators
if sv_detections and SUPERVISION_AVAILABLE:
    import supervision as sv

    box_annotator = sv.BoxAnnotator()
    label_annotator = sv.LabelAnnotator()

    annotated_frame = box_annotator.annotate(frame, sv_detections)
    annotated_frame = label_annotator.annotate(annotated_frame, sv_detections)
```

### 4. Advanced Tracking Workflow

```python
from showcase_framework import DetectionSourceFactory, SupervisionTracker

# Create high-sensitivity tracker
tracker = DetectionSourceFactory.create_tracker(
    tracker_type='bytetrack',
    sensitivity='high'
)

# Create processor with custom configuration
processor = DetectionSourceFactory.create_enhanced_processor(
    conf_threshold=0.2,
    enable_supervision=True,
    enable_tracking=True,
    tracking_params={
        'track_activation_threshold': 0.15,
        'lost_track_buffer': 60,
        'minimum_matching_threshold': 0.7,
        'enable_smoothing': True
    }
)

# Use in processing loop
for frame_packet in frame_stream:
    # Get detections with tracking
    sv_detections = processor.get_supervision_detections(
        frame_packet,
        source_format='hailo'
    )

    if sv_detections and hasattr(sv_detections, 'tracker_id'):
        print(f"Tracked {len(sv_detections)} objects with IDs: {sv_detections.tracker_id}")
```

### 5. Performance Monitoring and Caching

```python
from showcase_framework import SupervisionConverter

# Create converter with caching
converter = SupervisionConverter(enable_caching=True, cache_size=256)

# Process multiple frames
for frame_data in frames:
    sv_detections = converter.convert_to_supervision(
        detections=frame_data['detections'],
        source_format='auto',
        frame_shape=frame_data['shape']
    )

    # Monitor cache performance
    stats = converter.get_cache_stats()
    if stats['hit_rate'] < 0.5:
        print(f"Low cache hit rate: {stats['hit_rate']:.2f}")

# Clear cache when needed
converter.clear_cache()
```

## Integration with Existing Code

### Upgrading Existing FrameProcessor

```python
# Before: Basic FrameProcessor
class OldProcessor(FrameProcessor):
    def process(self, pkt):
        detections = self.parse_detections(pkt.model_out, pkt.frame_rgb.shape[:2])
        return self.draw_boxes(pkt.frame_rgb, detections)

# After: Enhanced with Supervision
class NewProcessor(EnhancedFrameProcessor):
    def process(self, pkt):
        # Automatic supervision integration
        if self.enable_supervision:
            return self.process_with_supervision(pkt, source_format='hailo')
        else:
            # Maintain backward compatibility
            detections = self.parse_detections(pkt.model_out, pkt.frame_rgb.shape[:2])
            return self.draw_boxes(pkt.frame_rgb, detections)
```

### Adding Tracking to Existing Pipeline

```python
# Minimal changes to add tracking
processor = EnhancedFrameProcessor(
    conf_threshold=0.3,
    enable_supervision=True,
    enable_tracking=True  # Just add this line!
)

# Everything else remains the same
pipeline = BufferedPipeline(source, model, processor)
```

## Best Practices

1. **Enable Caching**: Always enable caching for better performance
2. **Use Auto-Detection**: Let the system auto-detect formats when possible
3. **Monitor Performance**: Check cache hit rates and adjust cache size
4. **Graceful Degradation**: The system falls back gracefully when supervision is unavailable
5. **Thread Safety**: All components are thread-safe for pipeline use

## Troubleshooting

- **Supervision not available**: Install with `pip install supervision`
- **Poor tracking performance**: Adjust tracking sensitivity or thresholds
- **Memory issues**: Reduce cache size or disable caching
- **Format detection issues**: Specify source_format explicitly

The enhanced framework maintains full backward compatibility while providing
powerful new capabilities for professional computer vision workflows.
"""

# End of file
